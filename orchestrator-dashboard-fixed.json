{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"id": 1, "title": "Orchestrator Health & Status", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "collapsed": false}, {"id": 2, "title": "Active Step Executions", "description": "Current number of active step executions being processed by the orchestrator.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "orchestrator_active_step_executions_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"}", "instant": false, "legendFormat": "{{orchestrator_composite_key}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 1}}, {"id": 3, "title": "Flow Branch Terminations", "description": "Total number of flow branch terminations detected (completed flows).", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(orchestrator_flow_branch_terminations_total{orchestrator_composite_key=~\"$orchestrator_composite_key\", orchestrated_flow_version_name=~\"$orchestrated_flow_version_name\"})", "instant": false, "legendFormat": "Total Terminations", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "blue", "value": 0}]}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 1}}, {"id": 4, "title": "Flows Started", "description": "Total number of orchestration flows started.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(orchestrator_flows_started_Total_number_of_orchestration_flows_started_total{orchestrator_composite_key=~\"$orchestrator_composite_key\", orchestrated_flow_version_name=~\"$orchestrated_flow_version_name\"})", "instant": false, "legendFormat": "Started", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "blue", "value": 0}]}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 1}}, {"id": 5, "title": "Step Command Success Rate", "description": "Success rate percentage of step command publishing (successful vs total).", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "(sum(orchestrator_step_commands_successful_Total_number_of_step_commands_successfully_published_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"}) / sum(orchestrator_step_commands_published_Total_number_of_step_commands_published_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"})) * 100", "instant": false, "legendFormat": "Success Rate", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}, "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 1}}, {"id": 6, "title": "Step Command Publishing Metrics", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "collapsed": false}, {"id": 7, "title": "Step Commands Published (Total)", "description": "Total number of step commands published by the orchestrator to processors.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(orchestrator_step_commands_published_Total_number_of_step_commands_published_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"})", "instant": false, "legendFormat": "Total Published", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 10}}, {"id": 8, "title": "Step Commands Successful", "description": "Total number of step commands successfully published.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(orchestrator_step_commands_successful_Total_number_of_step_commands_successfully_published_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"})", "instant": false, "legendFormat": "Successful", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}]}}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 10}}, {"id": 9, "title": "Step Command Publishing by Step", "description": "Step command publishing rate by individual step version.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (step_version_name) (rate(orchestrator_step_commands_published_Total_number_of_step_commands_published_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"}[5m]))", "instant": false, "legendFormat": "{{step_version_name}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 10}}, {"id": 10, "title": "Step Command Publishing Duration", "description": "Distribution of step command publishing durations.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (le) (rate(orchestrator_step_command_publish_duration_seconds_Duration_of_step_command_publishing_operations_in_seconds_bucket{orchestrator_composite_key=~\"$orchestrator_composite_key\"}[5m]))", "instant": false, "legendFormat": "≤ {{le}}s", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops", "color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "stepAfter", "fillOpacity": 10}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 18}}, {"id": 11, "title": "Step Command Entities Count", "description": "Number of entities in published step commands.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (step_version_name) (rate(orchestrator_step_command_entities_count_Number_of_entities_in_published_step_commands_sum{orchestrator_composite_key=~\"$orchestrator_composite_key\"}[5m]))", "instant": false, "legendFormat": "{{step_version_name}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 18}}, {"id": 12, "title": "Activity Event Processing Metrics", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 26}, "collapsed": false}, {"id": 13, "title": "Activity Events Consumed (Total)", "description": "Total number of activity events consumed from processors.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(orchestrator_activity_events_consumed_Total_number_of_activity_events_consumed_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\", orchestrated_flow_version_name=~\"$orchestrated_flow_version_name\"})", "instant": false, "legendFormat": "Total Consumed", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 27}}, {"id": 14, "title": "Activity Events Successful", "description": "Total number of activity events successfully processed.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(orchestrator_activity_events_successful_Total_number_of_activity_events_successfully_processed_total{orchestrator_composite_key=~\"$orchestrator_composite_key\", orchestrated_flow_version_name=~\"$orchestrated_flow_version_name\"})", "instant": false, "legendFormat": "Successful", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}]}}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 27}}, {"id": 15, "title": "Activity Event Success Rate", "description": "Success rate percentage of activity event processing (successful vs total).", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "(sum(orchestrator_activity_events_successful_Total_number_of_activity_events_successfully_processed_total{orchestrator_composite_key=~\"$orchestrator_composite_key\", orchestrated_flow_version_name=~\"$orchestrated_flow_version_name\"}) / sum(orchestrator_activity_events_consumed_Total_number_of_activity_events_consumed_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\", orchestrated_flow_version_name=~\"$orchestrated_flow_version_name\"})) * 100", "instant": false, "legendFormat": "Success Rate", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}, "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 27}}, {"id": 16, "title": "Activity Event Processing by Step", "description": "Activity event processing rate by individual step version.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (step_version_name) (rate(orchestrator_activity_events_consumed_Total_number_of_activity_events_consumed_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\", orchestrated_flow_version_name=~\"$orchestrated_flow_version_name\"}[5m]))", "instant": false, "legendFormat": "{{step_version_name}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 35}}, {"id": 17, "title": "Activity Event Processing Duration", "description": "Distribution of activity event processing durations.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (le) (rate(orchestrator_activity_event_processing_duration_seconds_Duration_of_activity_event_processing_operations_in_seconds_bucket{orchestrator_composite_key=~\"$orchestrator_composite_key\", orchestrated_flow_version_name=~\"$orchestrated_flow_version_name\"}[5m]))", "instant": false, "legendFormat": "≤ {{le}}s", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops", "color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "stepAfter", "fillOpacity": 10}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 35}}, {"id": 18, "title": "Next Steps Generated", "description": "Number of next steps generated from activity events.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (step_version_name) (rate(orchestrator_next_steps_generated_count_Number_of_next_steps_generated_from_activity_events_sum{orchestrator_composite_key=~\"$orchestrator_composite_key\", orchestrated_flow_version_name=~\"$orchestrated_flow_version_name\"}[5m]))", "instant": false, "legendFormat": "{{step_version_name}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 43}}, {"id": 19, "title": "Flow Duration Distribution", "description": "Distribution of orchestration flow durations.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (le) (rate(orchestrator_flow_duration_seconds_Duration_of_orchestration_flow_operations_in_seconds_bucket{orchestrator_composite_key=~\"$orchestrator_composite_key\", orchestrated_flow_version_name=~\"$orchestrated_flow_version_name\"}[5m]))", "instant": false, "legendFormat": "≤ {{le}}s", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops", "color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "stepAfter", "fillOpacity": 10}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 51}}], "refresh": "30s", "schemaVersion": 38, "style": "dark", "tags": ["orchestrator", "metrics", "monitoring", "workflow"], "templating": {"list": [{"name": "orchestrator_composite_key", "type": "query", "label": "Orchestrator", "description": "Select one or more orchestrators to monitor", "query": "label_values(orchestrator_step_commands_published_Total_number_of_step_commands_published_by_the_orchestrator_total, orchestrator_composite_key)", "datasource": {"type": "prometheus"}, "refresh": 1, "sort": 1, "multi": true, "includeAll": true, "allValue": ".*", "current": {"selected": true, "text": "All", "value": "$__all"}, "options": [], "regex": "", "skipUrlSync": false}, {"name": "orchestrated_flow_version_name", "type": "query", "label": "Orchestrated Flow", "description": "Select one or more orchestrated flows to monitor", "query": "label_values(orchestrator_activity_events_consumed_Total_number_of_activity_events_consumed_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"}, orchestrated_flow_version_name)", "datasource": {"type": "prometheus"}, "refresh": 1, "sort": 1, "multi": true, "includeAll": true, "allValue": ".*", "current": {"selected": true, "text": "All", "value": "$__all"}, "options": [], "regex": "", "skipUrlSync": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Orchestrator Metrics Dashboard - Fixed", "uid": null, "version": 1, "weekStart": ""}