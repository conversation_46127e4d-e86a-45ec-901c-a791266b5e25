<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Manager.Orchestrator</name>
    </assembly>
    <members>
        <member name="T:Manager.Orchestrator.Consumers.ActivityExecutedEventConsumer">
            <summary>
            Consumer for ActivityExecutedEvent that handles workflow progression
            Manages the transition from completed steps to their next steps
            </summary>
        </member>
        <member name="M:Manager.Orchestrator.Consumers.ActivityExecutedEventConsumer.HandleFlowBranchTerminationAsync(Shared.MassTransit.Events.ActivityExecutedEvent)">
            <summary>
            Handles flow branch termination by deleting cache processor data
            </summary>
            <param name="activityEvent">The activity executed event</param>
        </member>
        <member name="M:Manager.Orchestrator.Consumers.ActivityExecutedEventConsumer.ProcessNextStepsAsync(Shared.MassTransit.Events.ActivityExecutedEvent,System.Collections.Generic.List{System.Guid},Manager.Orchestrator.Models.OrchestrationCacheModel)">
            <summary>
            Processes all next steps by copying cache data and publishing ExecuteActivityCommand
            </summary>
            <param name="activityEvent">The activity executed event</param>
            <param name="nextSteps">Collection of next step IDs</param>
            <param name="orchestrationData">Orchestration cache data</param>
        </member>
        <member name="M:Manager.Orchestrator.Consumers.ActivityExecutedEventConsumer.ProcessSingleNextStepAsync(Shared.MassTransit.Events.ActivityExecutedEvent,System.Guid,Manager.Orchestrator.Models.OrchestrationCacheModel)">
            <summary>
            Processes a single next step by copying cache data and publishing ExecuteActivityCommand
            </summary>
            <param name="activityEvent">The activity executed event</param>
            <param name="nextStepId">The next step ID to process</param>
            <param name="orchestrationData">Orchestration cache data</param>
        </member>
        <member name="M:Manager.Orchestrator.Consumers.ActivityExecutedEventConsumer.CopyCacheProcessorDataAsync(Shared.MassTransit.Events.ActivityExecutedEvent,System.Guid,System.Guid)">
            <summary>
            Copies cache processor data from source processor to destination processor
            </summary>
            <param name="activityEvent">The activity executed event</param>
            <param name="nextStepId">The next step ID</param>
            <param name="destinationProcessorId">The destination processor ID</param>
        </member>
        <member name="M:Manager.Orchestrator.Consumers.ActivityExecutedEventConsumer.ShouldExecuteStep(Shared.Entities.Enums.StepEntryCondition,Shared.MassTransit.Events.ActivityExecutedEvent,System.Boolean)">
            <summary>
            Determines if a step should be executed based on its entry condition and previous step result
            </summary>
            <param name="entryCondition">The entry condition of the step</param>
            <param name="activityEvent">The activity event from the previous step</param>
            <param name="isPreviousStepSuccess">Whether the previous step was successful</param>
            <returns>True if the step should be executed, false otherwise</returns>
        </member>
        <member name="M:Manager.Orchestrator.Consumers.ActivityExecutedEventConsumer.DeleteSourceCacheDataAsync(Shared.MassTransit.Events.ActivityExecutedEvent)">
            <summary>
            Deletes source cache data after processing
            </summary>
            <param name="activityEvent">The activity event</param>
        </member>
        <member name="T:Manager.Orchestrator.Consumers.ActivityFailedEventConsumer">
            <summary>
            Consumer for ActivityFailedEvent that handles workflow progression for failed activities
            Manages the transition from failed steps to their next steps based on entry conditions
            </summary>
        </member>
        <member name="M:Manager.Orchestrator.Consumers.ActivityFailedEventConsumer.HandleFlowBranchTerminationAsync(Shared.MassTransit.Events.ActivityFailedEvent)">
            <summary>
            Handles flow branch termination by deleting cache processor data
            </summary>
            <param name="activityEvent">The activity failed event</param>
        </member>
        <member name="M:Manager.Orchestrator.Consumers.ActivityFailedEventConsumer.ProcessNextStepsAsync(Shared.MassTransit.Events.ActivityFailedEvent,System.Collections.Generic.List{System.Guid},Manager.Orchestrator.Models.OrchestrationCacheModel)">
            <summary>
            Processes all next steps by copying cache data and publishing ExecuteActivityCommand
            </summary>
            <param name="activityEvent">The activity failed event</param>
            <param name="nextSteps">Collection of next step IDs</param>
            <param name="orchestrationData">Orchestration cache data</param>
        </member>
        <member name="M:Manager.Orchestrator.Consumers.ActivityFailedEventConsumer.ProcessSingleNextStepAsync(Shared.MassTransit.Events.ActivityFailedEvent,System.Guid,Manager.Orchestrator.Models.OrchestrationCacheModel)">
            <summary>
            Processes a single next step by copying cache data and publishing ExecuteActivityCommand
            </summary>
            <param name="activityEvent">The activity failed event</param>
            <param name="nextStepId">The next step ID to process</param>
            <param name="orchestrationData">Orchestration cache data</param>
        </member>
        <member name="M:Manager.Orchestrator.Consumers.ActivityFailedEventConsumer.CopyCacheProcessorDataAsync(Shared.MassTransit.Events.ActivityFailedEvent,System.Guid,System.Guid)">
            <summary>
            Copies cache processor data from source processor to destination processor
            </summary>
            <param name="activityEvent">The activity failed event</param>
            <param name="nextStepId">The next step ID</param>
            <param name="destinationProcessorId">The destination processor ID</param>
        </member>
        <member name="M:Manager.Orchestrator.Consumers.ActivityFailedEventConsumer.ShouldExecuteStep(Shared.Entities.Enums.StepEntryCondition,Shared.MassTransit.Events.ActivityFailedEvent,System.Boolean)">
            <summary>
            Determines if a step should be executed based on its entry condition and previous step result
            </summary>
            <param name="entryCondition">The entry condition of the step</param>
            <param name="activityEvent">The activity event from the previous step</param>
            <param name="isPreviousStepSuccess">Whether the previous step was successful</param>
            <returns>True if the step should be executed, false otherwise</returns>
        </member>
        <member name="M:Manager.Orchestrator.Consumers.ActivityFailedEventConsumer.DeleteSourceCacheDataAsync(Shared.MassTransit.Events.ActivityFailedEvent)">
            <summary>
            Deletes source cache data after processing
            </summary>
            <param name="activityEvent">The activity failed event</param>
        </member>
        <member name="M:Manager.Orchestrator.Controllers.OrchestrationController.Start(System.String)">
            <summary>
            Starts orchestration for the specified orchestrated flow ID
            </summary>
            <param name="orchestratedFlowId">The orchestrated flow ID to start</param>
            <returns>Success response</returns>
        </member>
        <member name="M:Manager.Orchestrator.Controllers.OrchestrationController.Stop(System.String)">
            <summary>
            Stops orchestration for the specified orchestrated flow ID
            </summary>
            <param name="orchestratedFlowId">The orchestrated flow ID to stop</param>
            <returns>Success response</returns>
        </member>
        <member name="M:Manager.Orchestrator.Controllers.OrchestrationController.GetStatus(System.String)">
            <summary>
            Gets orchestration status for the specified orchestrated flow ID
            </summary>
            <param name="orchestratedFlowId">The orchestrated flow ID to check</param>
            <returns>Orchestration status information</returns>
        </member>
        <member name="M:Manager.Orchestrator.Controllers.OrchestrationController.GetProcessorHealth(System.String)">
            <summary>
            Gets health status for a specific processor
            </summary>
            <param name="processorId">The processor ID to check</param>
            <returns>Processor health status</returns>
        </member>
        <member name="M:Manager.Orchestrator.Controllers.OrchestrationController.GetProcessorsHealthByOrchestratedFlow(System.String)">
            <summary>
            Gets health status for all processors in a specific orchestrated flow
            </summary>
            <param name="orchestratedFlowId">The orchestrated flow ID to check</param>
            <returns>Health status of all processors in the orchestrated flow</returns>
        </member>
        <member name="M:Manager.Orchestrator.Controllers.OrchestrationController.StartScheduler(System.String,Manager.Orchestrator.Models.StartSchedulerRequest)">
            <summary>
            Starts the scheduler for the specified orchestrated flow
            </summary>
            <param name="orchestratedFlowId">The orchestrated flow ID to schedule</param>
            <param name="request">Request containing cron expression</param>
            <returns>Success response</returns>
        </member>
        <member name="M:Manager.Orchestrator.Controllers.OrchestrationController.StopScheduler(System.String)">
            <summary>
            Stops the scheduler for the specified orchestrated flow
            </summary>
            <param name="orchestratedFlowId">The orchestrated flow ID to stop scheduling</param>
            <returns>Success response</returns>
        </member>
        <member name="T:Manager.Orchestrator.Jobs.OrchestratedFlowJob">
            <summary>
            Quartz job that executes orchestrated flow entry points on a scheduled basis.
            This job is responsible for triggering the execution of orchestrated flows based on cron expressions.
            </summary>
        </member>
        <member name="M:Manager.Orchestrator.Jobs.OrchestratedFlowJob.#ctor(Manager.Orchestrator.Services.IOrchestrationCacheService,Shared.Services.ICacheService,MassTransit.IBus,Microsoft.Extensions.Logging.ILogger{Manager.Orchestrator.Jobs.OrchestratedFlowJob},Manager.Orchestrator.Services.IOrchestratorMetricsService)">
            <summary>
            Initializes a new instance of the OrchestratedFlowJob class.
            </summary>
            <param name="orchestrationCacheService">Service for orchestration cache operations</param>
            <param name="rawCacheService"></param>
            <param name="bus">MassTransit bus for publishing commands</param>
            <param name="logger">Logger instance</param>
            <param name="metricsService">Metrics service for recording orchestrator metrics</param>
        </member>
        <member name="M:Manager.Orchestrator.Jobs.OrchestratedFlowJob.Execute(Quartz.IJobExecutionContext)">
            <summary>
            Executes the orchestrated flow job.
            This method is called by Quartz scheduler based on the configured cron expression.
            </summary>
            <param name="context">Job execution context containing job data</param>
            <returns>Task representing the asynchronous operation</returns>
        </member>
        <member name="M:Manager.Orchestrator.Jobs.OrchestratedFlowJob.ExecuteEntryPointsAsync(System.Guid,System.Collections.Generic.List{System.Guid},Manager.Orchestrator.Models.OrchestrationCacheModel,System.Guid)">
            <summary>
            Executes the entry points for the orchestrated flow.
            This method contains the core logic for publishing ExecuteActivityCommand for each entry point.
            </summary>
            <param name="orchestratedFlowId">ID of the orchestrated flow</param>
            <param name="entryPoints">List of entry point step IDs</param>
            <param name="orchestrationData">Cached orchestration data</param>
            <param name="correlationId">Correlation ID for this execution</param>
            <returns>Task representing the asynchronous operation</returns>
        </member>
        <member name="T:Manager.Orchestrator.Models.OrchestrationCacheModel">
            <summary>
            Complete orchestration data model for caching
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Models.OrchestrationCacheModel.OrchestratedFlowId">
            <summary>
            The orchestrated flow ID this cache entry is for
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Models.OrchestrationCacheModel.OrchestratedFlow">
            <summary>
            The orchestrated flow entity for this cache entry
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Models.OrchestrationCacheModel.StepManager">
            <summary>
            Step manager data containing processor IDs, step IDs, next step IDs, and step entities
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Models.OrchestrationCacheModel.AssignmentManager">
            <summary>
            Assignment manager data containing assignments by step ID
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Models.OrchestrationCacheModel.EntryPoints">
            <summary>
            List of entry point step IDs for this orchestrated flow.
            These are the steps that should be executed when starting the workflow.
            Calculated once during orchestration setup and cached for reuse.
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Models.OrchestrationCacheModel.CreatedAt">
            <summary>
            Timestamp when this cache entry was created
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Models.OrchestrationCacheModel.ExpiresAt">
            <summary>
            Timestamp when this cache entry expires
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Models.OrchestrationCacheModel.Version">
            <summary>
            Version of the cache model for future compatibility
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Models.OrchestrationCacheModel.IsExpired">
            <summary>
            Indicates if this cache entry has expired
            </summary>
        </member>
        <member name="T:Manager.Orchestrator.Models.StartSchedulerRequest">
            <summary>
            Request model for starting a scheduler for an orchestrated flow.
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Models.StartSchedulerRequest.CronExpression">
            <summary>
            Gets or sets the cron expression for scheduled execution.
            Example: "0 0 12 * * ?" for daily at noon
            </summary>
        </member>
        <member name="T:Manager.Orchestrator.Services.IManagerHttpClient">
            <summary>
            Interface for HTTP communication with other entity managers
            </summary>
        </member>
        <member name="M:Manager.Orchestrator.Services.IManagerHttpClient.GetOrchestratedFlowAsync(System.Guid)">
            <summary>
            Retrieves the orchestrated flow entity by ID
            </summary>
            <param name="orchestratedFlowId">The orchestrated flow ID</param>
            <returns>The orchestrated flow entity</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.IManagerHttpClient.GetStepManagerDataAsync(System.Guid)">
            <summary>
            Retrieves all step-related data for the orchestrated flow
            </summary>
            <param name="workflowId">The workflow ID from the orchestrated flow</param>
            <returns>Step manager model with all step data</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.IManagerHttpClient.GetAssignmentManagerDataAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            Retrieves all assignment-related data for the orchestrated flow
            </summary>
            <param name="assignmentIds">List of assignment IDs from the orchestrated flow</param>
            <returns>Assignment manager model with all assignment data</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.IManagerHttpClient.GetSchemaDefinitionAsync(System.Guid)">
            <summary>
            Retrieves schema definition by schema ID
            </summary>
            <param name="schemaId">The schema ID</param>
            <returns>Schema definition string</returns>
        </member>
        <member name="T:Manager.Orchestrator.Services.IOrchestrationCacheService">
            <summary>
            Interface for orchestration cache service operations following ProcessorHealthMonitor pattern
            </summary>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestrationCacheService.StoreOrchestrationDataAsync(System.Guid,Manager.Orchestrator.Models.OrchestrationCacheModel,System.Nullable{System.TimeSpan})">
            <summary>
            Stores orchestration data in cache
            </summary>
            <param name="orchestratedFlowId">The orchestrated flow ID as cache key</param>
            <param name="orchestrationData">The complete orchestration data to cache</param>
            <param name="ttl">Time-to-live for the cache entry</param>
            <returns>Task representing the cache operation</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestrationCacheService.GetOrchestrationDataAsync(System.Guid)">
            <summary>
            Retrieves orchestration data from cache
            </summary>
            <param name="orchestratedFlowId">The orchestrated flow ID as cache key</param>
            <returns>Cached orchestration data or null if not found/expired</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestrationCacheService.RemoveOrchestrationDataAsync(System.Guid)">
            <summary>
            Removes orchestration data from cache
            </summary>
            <param name="orchestratedFlowId">The orchestrated flow ID as cache key</param>
            <returns>Task representing the cache operation</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestrationCacheService.ExistsAndValidAsync(System.Guid)">
            <summary>
            Checks if orchestration data exists in cache and is not expired
            </summary>
            <param name="orchestratedFlowId">The orchestrated flow ID as cache key</param>
            <returns>True if data exists and is valid, false otherwise</returns>
        </member>
        <member name="T:Manager.Orchestrator.Services.IOrchestrationSchedulerService">
            <summary>
            Service interface for managing Quartz schedulers for orchestrated flows.
            Provides functionality to start, stop, and manage scheduled execution of orchestrated flows.
            </summary>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestrationSchedulerService.StartSchedulerAsync(System.Guid,System.String,System.Threading.CancellationToken)">
            <summary>
            Starts the scheduler for the specified orchestrated flow.
            Creates a new Quartz job with the provided cron expression.
            </summary>
            <param name="orchestratedFlowId">ID of the orchestrated flow to schedule</param>
            <param name="cronExpression">Cron expression defining the schedule</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task representing the asynchronous operation</returns>
            <exception cref="T:System.ArgumentException">Thrown when cron expression is invalid</exception>
            <exception cref="T:System.InvalidOperationException">Thrown when scheduler is already running for this flow</exception>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestrationSchedulerService.StopSchedulerAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Stops the scheduler for the specified orchestrated flow.
            Removes the Quartz job and cancels any pending executions.
            </summary>
            <param name="orchestratedFlowId">ID of the orchestrated flow to stop scheduling</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task representing the asynchronous operation</returns>
            <exception cref="T:System.InvalidOperationException">Thrown when no scheduler is running for this flow</exception>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestrationSchedulerService.UpdateSchedulerAsync(System.Guid,System.String,System.Threading.CancellationToken)">
            <summary>
            Updates the scheduler for the specified orchestrated flow with a new cron expression.
            If scheduler is running, it will be stopped and restarted with the new schedule.
            </summary>
            <param name="orchestratedFlowId">ID of the orchestrated flow to update</param>
            <param name="cronExpression">New cron expression defining the schedule</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task representing the asynchronous operation</returns>
            <exception cref="T:System.ArgumentException">Thrown when cron expression is invalid</exception>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestrationSchedulerService.IsSchedulerRunningAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Checks if a scheduler is currently running for the specified orchestrated flow.
            </summary>
            <param name="orchestratedFlowId">ID of the orchestrated flow to check</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if scheduler is running, false otherwise</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestrationSchedulerService.GetSchedulerCronExpressionAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets the current cron expression for the specified orchestrated flow scheduler.
            </summary>
            <param name="orchestratedFlowId">ID of the orchestrated flow</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Current cron expression, or null if no scheduler is running</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestrationSchedulerService.GetNextExecutionTimeAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets the next scheduled execution time for the specified orchestrated flow.
            </summary>
            <param name="orchestratedFlowId">ID of the orchestrated flow</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Next execution time, or null if no scheduler is running</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestrationSchedulerService.ValidateCronExpression(System.String)">
            <summary>
            Validates if the provided cron expression is valid.
            </summary>
            <param name="cronExpression">Cron expression to validate</param>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestrationSchedulerService.GetScheduledFlowsAsync(System.Threading.CancellationToken)">
            <summary>
            Gets a list of all currently scheduled orchestrated flows.
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of orchestrated flow IDs that have active schedulers</returns>
        </member>
        <member name="T:Manager.Orchestrator.Services.IOrchestrationService">
            <summary>
            Interface for orchestration business logic service
            </summary>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestrationService.StartOrchestrationAsync(System.Guid)">
            <summary>
            Starts orchestration for the given orchestrated flow ID
            Retrieves all required data from managers and stores in cache
            </summary>
            <param name="orchestratedFlowId">The orchestrated flow ID to start</param>
            <returns>Task representing the start operation</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestrationService.StopOrchestrationAsync(System.Guid)">
            <summary>
            Stops orchestration for the given orchestrated flow ID
            Removes data from cache and performs cleanup
            </summary>
            <param name="orchestratedFlowId">The orchestrated flow ID to stop</param>
            <returns>Task representing the stop operation</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestrationService.GetOrchestrationStatusAsync(System.Guid)">
            <summary>
            Gets orchestration status for the given orchestrated flow ID
            </summary>
            <param name="orchestratedFlowId">The orchestrated flow ID to check</param>
            <returns>Orchestration status information</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestrationService.GetProcessorHealthAsync(System.Guid)">
            <summary>
            Gets health status for a specific processor
            </summary>
            <param name="processorId">The processor ID to check</param>
            <returns>Processor health status or null if not found</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestrationService.GetProcessorsHealthByOrchestratedFlowAsync(System.Guid)">
            <summary>
            Gets health status for all processors in a specific orchestrated flow
            </summary>
            <param name="orchestratedFlowId">The orchestrated flow ID to check</param>
            <returns>Dictionary of processor health statuses or null if orchestrated flow not found in cache</returns>
        </member>
        <member name="T:Manager.Orchestrator.Services.OrchestrationStatusModel">
            <summary>
            Model representing orchestration status
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Services.OrchestrationStatusModel.OrchestratedFlowId">
            <summary>
            The orchestrated flow ID
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Services.OrchestrationStatusModel.IsActive">
            <summary>
            Indicates if orchestration is active (data exists in cache)
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Services.OrchestrationStatusModel.StartedAt">
            <summary>
            Timestamp when orchestration was started
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Services.OrchestrationStatusModel.ExpiresAt">
            <summary>
            Timestamp when orchestration data expires
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Services.OrchestrationStatusModel.StepCount">
            <summary>
            Number of steps in the orchestration
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Services.OrchestrationStatusModel.AssignmentCount">
            <summary>
            Number of assignments in the orchestration
            </summary>
        </member>
        <member name="T:Manager.Orchestrator.Services.IOrchestratorMetricsLabelService">
            <summary>
            Service for generating consistent orchestrator metrics labels across all metric types.
            Ensures all metrics use the same orchestrator_composite_key and labeling standards.
            Follows the same design pattern as ProcessorMetricsLabelService for consistency.
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Services.IOrchestratorMetricsLabelService.OrchestratorCompositeKey">
            <summary>
            The standardized orchestrator composite key (e.g., "1.0.0_OrchestratorManager")
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Services.IOrchestratorMetricsLabelService.OrchestratorInstanceId">
            <summary>
            The orchestrator instance ID (unique per orchestrator instance)
            </summary>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestratorMetricsLabelService.GetStandardLabels">
            <summary>
            Gets the standard base labels that should be included in all orchestrator metrics
            </summary>
            <returns>KeyValuePair array with orchestrator_composite_key, orchestrator_name, orchestrator_version, orchestrator_id, environment</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestratorMetricsLabelService.GetStepPublishLabels(System.String,System.String,System.String)">
            <summary>
            Gets labels for step command publishing metrics
            </summary>
            <param name="stepVersionName">Step version name (e.g., "1.1.0_DataValidationStep")</param>
            <param name="operation">Operation type (e.g., "publish_step_command")</param>
            <param name="status">Operation status (e.g., "success", "failed")</param>
            <returns>KeyValuePair array with standard labels plus step-specific labels</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestratorMetricsLabelService.GetStepConsumptionLabels(System.String,System.String,System.String,System.String)">
            <summary>
            Gets labels for activity event consumption metrics
            </summary>
            <param name="stepVersionName">Step version name (e.g., "1.1.0_DataValidationStep")</param>
            <param name="orchestratedFlowVersionName">Orchestrated flow version name (e.g., "2.0.0_DataProcessingFlow")</param>
            <param name="operation">Operation type (e.g., "process_activity_event")</param>
            <param name="status">Operation status (e.g., "success", "failed")</param>
            <returns>KeyValuePair array with standard labels plus consumption-specific labels</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestratorMetricsLabelService.GetFlowLabels(System.String,System.String,System.String)">
            <summary>
            Gets labels for orchestration flow metrics
            </summary>
            <param name="orchestratedFlowVersionName">Orchestrated flow version name (e.g., "2.0.0_DataProcessingFlow")</param>
            <param name="operation">Operation type (e.g., "execute_entry_points", "flow_completion")</param>
            <param name="status">Operation status (e.g., "success", "failed")</param>
            <returns>KeyValuePair array with standard labels plus flow-specific labels</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestratorMetricsLabelService.GetCacheLabels(System.String,System.String)">
            <summary>
            Gets labels for cache operation metrics
            </summary>
            <param name="operationType">Type of cache operation (e.g., "read", "write", "delete")</param>
            <param name="status">Operation status (e.g., "success", "failed")</param>
            <returns>KeyValuePair array with standard labels plus cache-specific labels</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestratorMetricsLabelService.GetHealthLabels(System.String,System.String)">
            <summary>
            Gets labels for health check metrics
            </summary>
            <param name="healthCheckName">Name of the health check (e.g., "cache", "messagebus")</param>
            <param name="healthCheckStatus">Status of the health check (e.g., "Healthy", "Unhealthy")</param>
            <returns>KeyValuePair array with standard labels plus health-specific labels</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestratorMetricsLabelService.GetSystemLabels">
            <summary>
            Gets labels for system/performance metrics
            </summary>
            <returns>KeyValuePair array with standard labels for system metrics</returns>
        </member>
        <member name="T:Manager.Orchestrator.Services.IOrchestratorMetricsService">
            <summary>
            Service for recording orchestrator metrics for monitoring publishing to steps and consuming from steps.
            Provides comprehensive metrics for orchestration operations following the processor metrics design pattern.
            </summary>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestratorMetricsService.RecordStepCommandPublished(System.Boolean,System.TimeSpan,System.String,System.Int32)">
            <summary>
            Records step command publishing metrics
            </summary>
            <param name="success">Whether the step command was published successfully</param>
            <param name="duration">Duration of the publishing operation</param>
            <param name="stepVersionName">Step version name (e.g., "1.1.0_DataValidationStep")</param>
            <param name="entitiesCount">Number of entities in the step command</param>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestratorMetricsService.RecordEntryPointExecution(System.Boolean,System.TimeSpan,System.String,System.Int32)">
            <summary>
            Records entry point execution metrics
            </summary>
            <param name="success">Whether the entry point execution was successful</param>
            <param name="duration">Duration of the entry point execution</param>
            <param name="orchestratedFlowVersionName">Orchestrated flow version name (e.g., "2.0.0_DataProcessingFlow")</param>
            <param name="entryPointCount">Number of entry points executed</param>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestratorMetricsService.UpdateActiveStepExecutions(System.Int64)">
            <summary>
            Updates the current number of active step executions
            </summary>
            <param name="count">Current active step execution count</param>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestratorMetricsService.RecordActivityEventProcessed(System.Boolean,System.TimeSpan,System.String,System.String,System.Int32,System.Boolean)">
            <summary>
            Records activity event processing metrics
            </summary>
            <param name="success">Whether the activity event was processed successfully</param>
            <param name="duration">Duration of the event processing</param>
            <param name="stepVersionName">Step version name (e.g., "1.1.0_DataValidationStep")</param>
            <param name="orchestratedFlowVersionName">Orchestrated flow version name (e.g., "2.0.0_DataProcessingFlow")</param>
            <param name="nextStepsCount">Number of next steps generated</param>
            <param name="isFlowTermination">Whether this event represents a flow termination</param>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestratorMetricsService.RecordOrchestrationFlow(System.Boolean,System.TimeSpan,System.String,System.String)">
            <summary>
            Records orchestration flow lifecycle metrics
            </summary>
            <param name="success">Whether the flow operation was successful</param>
            <param name="duration">Duration of the flow operation</param>
            <param name="orchestratedFlowVersionName">Orchestrated flow version name (e.g., "2.0.0_DataProcessingFlow")</param>
            <param name="operation">Type of flow operation (e.g., "start", "complete", "fail")</param>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestratorMetricsService.RecordCacheOperation(System.Boolean,System.TimeSpan,System.String)">
            <summary>
            Records cache operation metrics
            </summary>
            <param name="success">Whether the cache operation was successful</param>
            <param name="duration">Duration of the cache operation</param>
            <param name="operationType">Type of cache operation (e.g., "read", "write", "delete")</param>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestratorMetricsService.UpdateActiveCacheEntries(System.Int64)">
            <summary>
            Updates the current number of active cache entries
            </summary>
            <param name="count">Current active cache entry count</param>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestratorMetricsService.RecordHealthStatus(System.Int32)">
            <summary>
            Records orchestrator health status
            </summary>
            <param name="status">Health status (0=Healthy, 1=Degraded, 2=Unhealthy)</param>
        </member>
        <member name="M:Manager.Orchestrator.Services.IOrchestratorMetricsService.RecordHealthCheck(System.String,System.String,System.TimeSpan)">
            <summary>
            Records health check metrics
            </summary>
            <param name="healthCheckName">Name of the health check</param>
            <param name="healthCheckStatus">Status of the health check</param>
            <param name="duration">Duration of the health check</param>
        </member>
        <member name="T:Manager.Orchestrator.Services.ManagerHttpClient">
            <summary>
            HTTP client for communication with other entity managers with resilience patterns
            </summary>
        </member>
        <member name="T:Manager.Orchestrator.Services.OrchestrationCacheService">
            <summary>
            Orchestration cache service implementation following ProcessorHealthMonitor pattern
            </summary>
        </member>
        <member name="T:Manager.Orchestrator.Services.OrchestrationSchedulerService">
            <summary>
            Service for managing Quartz schedulers for orchestrated flows.
            Handles starting, stopping, and managing scheduled execution of orchestrated flows.
            </summary>
        </member>
        <member name="M:Manager.Orchestrator.Services.OrchestrationSchedulerService.#ctor(Quartz.ISchedulerFactory,Microsoft.Extensions.Logging.ILogger{Manager.Orchestrator.Services.OrchestrationSchedulerService})">
            <summary>
            Initializes a new instance of the OrchestrationSchedulerService class.
            </summary>
            <param name="schedulerFactory">Quartz scheduler factory</param>
            <param name="logger">Logger instance</param>
        </member>
        <member name="M:Manager.Orchestrator.Services.OrchestrationSchedulerService.EnsureSchedulerStartedAsync">
            <summary>
            Ensures the scheduler is initialized and started.
            </summary>
            <returns>Task representing the asynchronous operation</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.OrchestrationSchedulerService.CreateJobKey(System.Guid)">
            <summary>
            Creates a job key for the specified orchestrated flow.
            </summary>
            <param name="orchestratedFlowId">ID of the orchestrated flow</param>
            <returns>Job key</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.OrchestrationSchedulerService.CreateTriggerKey(System.Guid)">
            <summary>
            Creates a trigger key for the specified orchestrated flow.
            </summary>
            <param name="orchestratedFlowId">ID of the orchestrated flow</param>
            <returns>Trigger key</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.OrchestrationSchedulerService.StartSchedulerAsync(System.Guid,System.String,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Manager.Orchestrator.Services.OrchestrationSchedulerService.StopSchedulerAsync(System.Guid,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Manager.Orchestrator.Services.OrchestrationSchedulerService.UpdateSchedulerAsync(System.Guid,System.String,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Manager.Orchestrator.Services.OrchestrationSchedulerService.IsSchedulerRunningAsync(System.Guid,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Manager.Orchestrator.Services.OrchestrationSchedulerService.GetSchedulerCronExpressionAsync(System.Guid,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Manager.Orchestrator.Services.OrchestrationSchedulerService.GetNextExecutionTimeAsync(System.Guid,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Manager.Orchestrator.Services.OrchestrationSchedulerService.ValidateCronExpression(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Manager.Orchestrator.Services.OrchestrationSchedulerService.GetScheduledFlowsAsync(System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="T:Manager.Orchestrator.Services.OrchestrationService">
            <summary>
            Main orchestration business logic service
            </summary>
        </member>
        <member name="M:Manager.Orchestrator.Services.OrchestrationService.ValidateProcessorHealthAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            Validates the health of all processors required for orchestration
            </summary>
            <param name="processorIds">Collection of processor IDs to validate</param>
            <exception cref="T:System.InvalidOperationException">Thrown when one or more processors are unhealthy</exception>
        </member>
        <member name="M:Manager.Orchestrator.Services.OrchestrationService.FindEntryPoints(Shared.Models.StepManagerModel)">
            <summary>
            Finds entry points in the workflow by identifying steps that are not referenced as next steps
            </summary>
            <param name="stepManagerData">Step manager data containing step and next step information</param>
            <returns>Collection of step IDs that are entry points</returns>
        </member>
        <member name="M:Manager.Orchestrator.Services.OrchestrationService.ValidateEntryPoints(System.Collections.Generic.List{System.Guid})">
            <summary>
            Validates that the workflow has valid entry points and is not cyclical
            </summary>
            <param name="entryPoints">Collection of entry point step IDs</param>
            <exception cref="T:System.InvalidOperationException">Thrown when workflow has no entry points (indicating cyclicity)</exception>
        </member>
        <member name="M:Manager.Orchestrator.Services.OrchestrationService.GetCurrentCorrelationIdOrGenerate">
            <summary>
            Gets correlation ID from current context or generates a new one if none exists.
            This is appropriate for workflow start operations.
            </summary>
        </member>
        <member name="M:Manager.Orchestrator.Services.OrchestrationService.StartSchedulerIfConfiguredAsync(System.Guid,Shared.Entities.OrchestratedFlowEntity)">
            <summary>
            Starts the scheduler for the orchestrated flow if cron expression is configured and enabled
            </summary>
            <param name="orchestratedFlowId">The orchestrated flow ID</param>
            <param name="orchestratedFlow">The orchestrated flow entity</param>
        </member>
        <member name="M:Manager.Orchestrator.Services.OrchestrationService.StopSchedulerIfRunningAsync(System.Guid)">
            <summary>
            Stops the scheduler for the orchestrated flow if it's running
            </summary>
            <param name="orchestratedFlowId">The orchestrated flow ID</param>
        </member>
        <member name="T:Manager.Orchestrator.Services.OrchestratorHealthMonitor">
            <summary>
            Background service that monitors orchestrator health following the processor health monitoring pattern.
            Performs periodic health checks and records health status and cache metrics.
            </summary>
        </member>
        <member name="T:Manager.Orchestrator.Services.OrchestratorHealthMonitorConfiguration">
            <summary>
            Configuration for orchestrator health monitoring following processor pattern
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Services.OrchestratorHealthMonitorConfiguration.Enabled">
            <summary>
            Whether health monitoring is enabled
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Services.OrchestratorHealthMonitorConfiguration.HealthCheckInterval">
            <summary>
            Interval between health checks
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Services.OrchestratorHealthMonitorConfiguration.CacheMapName">
            <summary>
            Cache map name for orchestration data
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Services.OrchestratorHealthMonitorConfiguration.LogHealthChecks">
            <summary>
            Whether to log health check results
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Services.OrchestratorHealthMonitorConfiguration.LogLevel">
            <summary>
            Log level for health check logging (Information, Warning, Error, Debug)
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Services.OrchestratorHealthMonitorConfiguration.ContinueOnFailure">
            <summary>
            Whether to continue on health check failures
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Services.OrchestratorHealthMonitorConfiguration.MaxRetries">
            <summary>
            Maximum number of retries for cache operations
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Services.OrchestratorHealthMonitorConfiguration.RetryDelay">
            <summary>
            Delay between retry attempts
            </summary>
        </member>
        <member name="P:Manager.Orchestrator.Services.OrchestratorHealthMonitorConfiguration.UseExponentialBackoff">
            <summary>
            Whether to use exponential backoff for retries
            </summary>
        </member>
        <member name="T:Manager.Orchestrator.Services.OrchestratorMetricsLabelService">
            <summary>
            Centralized service for generating consistent orchestrator metrics labels.
            Reads configuration from ManagerConfiguration and ensures all metrics use the same labeling standards.
            Follows the same design pattern as ProcessorMetricsLabelService for consistency.
            </summary>
        </member>
        <member name="T:Manager.Orchestrator.Services.OrchestratorMetricsService">
            <summary>
            Service for recording orchestrator metrics for monitoring publishing to steps and consuming from steps.
            Provides comprehensive metrics for orchestration operations following the processor metrics design pattern.
            Uses IOrchestratorMetricsLabelService for consistent labeling across all metrics.
            </summary>
        </member>
    </members>
</doc>
