// Auto-generated during build - DO NOT MODIFY
using System;

namespace Processor.File;

/// <summary>
/// Auto-generated class containing SHA-256 hash of processor implementation files.
/// Used for runtime integrity validation to ensure version consistency.
/// </summary>
public static class ProcessorImplementationHash
{
    /// <summary>
    /// SHA-256 hash of the processor implementation file: FileProcessorApplication.cs
    /// </summary>
    public static string Hash => "dd40afb23487070edca1c180b25d26a6155fbdccf42c017e1ac033a75e96ff49";

    /// <summary>
    /// Processor version from assembly information.
    /// </summary>
    public const string Version = "3.0.0";

    /// <summary>
    /// Processor name from assembly information.
    /// </summary>
    public const string Name = "Processor.File";

    /// <summary>
    /// Timestamp when hash was generated.
    /// </summary>
    public const string GeneratedAt = "2025-06-21T11:46:42.927Z";

    /// <summary>
    /// Source file that was hashed.
    /// </summary>
    public const string SourceFile = "FileProcessorApplication.cs";
}
