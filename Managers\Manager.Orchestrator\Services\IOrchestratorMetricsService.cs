namespace Manager.Orchestrator.Services;

/// <summary>
/// Service for recording orchestrator metrics for monitoring publishing to steps and consuming from steps.
/// Provides comprehensive metrics for orchestration operations following the processor metrics design pattern.
/// </summary>
public interface IOrchestratorMetricsService : IDisposable
{
    // Step Publishing Metrics
    
    /// <summary>
    /// Records step command publishing metrics (optimized for anomaly detection)
    /// </summary>
    /// <param name="success">Whether the step command was published successfully</param>
    /// <param name="stepVersionName">Step version name (e.g., "1.1.0_DataValidationStep")</param>
    void RecordStepCommandPublished(bool success, string stepVersionName);

    /// <summary>
    /// Updates the current number of active step executions
    /// </summary>
    /// <param name="count">Current active step execution count</param>
    void UpdateActiveStepExecutions(long count);

    // Step Consumption Metrics
    
    /// <summary>
    /// Records activity event processing metrics (optimized for anomaly detection)
    /// </summary>
    /// <param name="success">Whether the activity event was processed successfully</param>
    /// <param name="stepVersionName">Step version name (e.g., "1.1.0_DataValidationStep")</param>
    /// <param name="orchestratedFlowVersionName">Orchestrated flow version name (e.g., "2.0.0_DataProcessingFlow")</param>
    /// <param name="isFlowTermination">Whether this event represents a flow termination</param>
    void RecordActivityEventProcessed(bool success, string stepVersionName, string orchestratedFlowVersionName, bool isFlowTermination = false);

    // Flow Lifecycle Metrics
    
    /// <summary>
    /// Records orchestration flow lifecycle metrics
    /// </summary>
    /// <param name="success">Whether the flow operation was successful</param>
    /// <param name="duration">Duration of the flow operation</param>
    /// <param name="orchestratedFlowVersionName">Orchestrated flow version name (e.g., "2.0.0_DataProcessingFlow")</param>
    /// <param name="operation">Type of flow operation (e.g., "start", "complete", "fail")</param>
    void RecordOrchestrationFlow(bool success, TimeSpan duration, string orchestratedFlowVersionName, string operation);

    // Cache Operations Metrics
    
    /// <summary>
    /// Records cache operation metrics
    /// </summary>
    /// <param name="success">Whether the cache operation was successful</param>
    /// <param name="duration">Duration of the cache operation</param>
    /// <param name="operationType">Type of cache operation (e.g., "read", "write", "delete")</param>
    void RecordCacheOperation(bool success, TimeSpan duration, string operationType);

    /// <summary>
    /// Updates the current number of active cache entries
    /// </summary>
    /// <param name="count">Current active cache entry count</param>
    void UpdateActiveCacheEntries(long count);

    // Health Metrics
    
    /// <summary>
    /// Records orchestrator health status
    /// </summary>
    /// <param name="status">Health status (0=Healthy, 1=Degraded, 2=Unhealthy)</param>
    void RecordHealthStatus(int status);

    /// <summary>
    /// Records health check metrics
    /// </summary>
    /// <param name="healthCheckName">Name of the health check</param>
    /// <param name="healthCheckStatus">Status of the health check</param>
    /// <param name="duration">Duration of the health check</param>
    void RecordHealthCheck(string healthCheckName, string healthCheckStatus, TimeSpan duration);
}
