{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\Design40\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{8F2A1B3C-4D5E-6F78-9A0B-1C2D3E4F5A6B}|Managers\\Manager.Orchestrator\\Manager.Orchestrator.csproj|c:\\users\\<USER>\\source\\repos\\design40\\managers\\manager.orchestrator\\services\\orchestrationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8F2A1B3C-4D5E-6F78-9A0B-1C2D3E4F5A6B}|Managers\\Manager.Orchestrator\\Manager.Orchestrator.csproj|solutionrelative:managers\\manager.orchestrator\\services\\orchestrationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8F2A1B3C-4D5E-6F78-9A0B-1C2D3E4F5A6B}|Managers\\Manager.Orchestrator\\Manager.Orchestrator.csproj|c:\\users\\<USER>\\source\\repos\\design40\\managers\\manager.orchestrator\\services\\orchestratormetricsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8F2A1B3C-4D5E-6F78-9A0B-1C2D3E4F5A6B}|Managers\\Manager.Orchestrator\\Manager.Orchestrator.csproj|solutionrelative:managers\\manager.orchestrator\\services\\orchestratormetricsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8F2A1B3C-4D5E-6F78-9A0B-1C2D3E4F5A6B}|Managers\\Manager.Orchestrator\\Manager.Orchestrator.csproj|c:\\users\\<USER>\\source\\repos\\design40\\managers\\manager.orchestrator\\consumers\\activityexecutedeventconsumer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8F2A1B3C-4D5E-6F78-9A0B-1C2D3E4F5A6B}|Managers\\Manager.Orchestrator\\Manager.Orchestrator.csproj|solutionrelative:managers\\manager.orchestrator\\consumers\\activityexecutedeventconsumer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8F2A1B3C-4D5E-6F78-9A0B-1C2D3E4F5A6B}|Managers\\Manager.Orchestrator\\Manager.Orchestrator.csproj|c:\\users\\<USER>\\source\\repos\\design40\\managers\\manager.orchestrator\\jobs\\orchestratedflowjob.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8F2A1B3C-4D5E-6F78-9A0B-1C2D3E4F5A6B}|Managers\\Manager.Orchestrator\\Manager.Orchestrator.csproj|solutionrelative:managers\\manager.orchestrator\\jobs\\orchestratedflowjob.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "ActivityExecutedEventConsumer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design40\\Managers\\Manager.Orchestrator\\Consumers\\ActivityExecutedEventConsumer.cs", "RelativeDocumentMoniker": "Managers\\Manager.Orchestrator\\Consumers\\ActivityExecutedEventConsumer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design40\\Managers\\Manager.Orchestrator\\Consumers\\ActivityExecutedEventConsumer.cs", "RelativeToolTip": "Managers\\Manager.Orchestrator\\Consumers\\ActivityExecutedEventConsumer.cs", "ViewState": "AgIAAOEAAAAAAAAAAAAkwDABAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T10:46:11.139Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "OrchestratedFlowJob.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design40\\Managers\\Manager.Orchestrator\\Jobs\\OrchestratedFlowJob.cs", "RelativeDocumentMoniker": "Managers\\Manager.Orchestrator\\Jobs\\OrchestratedFlowJob.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design40\\Managers\\Manager.Orchestrator\\Jobs\\OrchestratedFlowJob.cs", "RelativeToolTip": "Managers\\Manager.Orchestrator\\Jobs\\OrchestratedFlowJob.cs", "ViewState": "AgIAAA8AAAAAAAAAAAD4vyAAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T10:29:04.075Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "OrchestrationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design40\\Managers\\Manager.Orchestrator\\Services\\OrchestrationService.cs", "RelativeDocumentMoniker": "Managers\\Manager.Orchestrator\\Services\\OrchestrationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design40\\Managers\\Manager.Orchestrator\\Services\\OrchestrationService.cs*", "RelativeToolTip": "Managers\\Manager.Orchestrator\\Services\\OrchestrationService.cs*", "ViewState": "AgIAACICAAAAAAAAAAAgwFICAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T10:23:53.816Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "OrchestratorMetricsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design40\\Managers\\Manager.Orchestrator\\Services\\OrchestratorMetricsService.cs", "RelativeDocumentMoniker": "Managers\\Manager.Orchestrator\\Services\\OrchestratorMetricsService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design40\\Managers\\Manager.Orchestrator\\Services\\OrchestratorMetricsService.cs", "RelativeToolTip": "Managers\\Manager.Orchestrator\\Services\\OrchestratorMetricsService.cs", "ViewState": "AgIAACIAAAAAAAAAAAApwNcAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T08:59:50.805Z", "EditorCaption": ""}]}]}]}