using System.Diagnostics.Metrics;
using Microsoft.Extensions.Options;
using Shared.Models;
using Shared.Extensions;
using Shared.Correlation;

namespace Manager.Orchestrator.Services;

/// <summary>
/// Service for recording orchestrator metrics for monitoring publishing to steps and consuming from steps.
/// Provides comprehensive metrics for orchestration operations following the processor metrics design pattern.
/// Uses IOrchestratorMetricsLabelService for consistent labeling across all metrics.
/// </summary>
public class OrchestratorMetricsService : IOrchestratorMetricsService, IDisposable
{
    private readonly ManagerConfiguration _config;
    private readonly ILogger<OrchestratorMetricsService> _logger;
    private readonly IOrchestratorMetricsLabelService _labelService;
    private readonly Meter _meter;

    // Step Publishing Metrics (Core for Anomaly Detection)
    private readonly Counter<long> _stepCommandsPublishedCounter;
    private readonly Counter<long> _stepCommandsSuccessfulCounter;
    private readonly Counter<long> _stepCommandsFailedCounter;
    private readonly Gauge<long> _activeStepExecutionsGauge;

    // Step Consumption Metrics (Core for Anomaly Detection)
    private readonly Counter<long> _activityEventsConsumedCounter;
    private readonly Counter<long> _activityEventsSuccessfulCounter;
    private readonly Counter<long> _activityEventsFailedCounter;
    private readonly Counter<long> _flowBranchTerminationsCounter;

    // Flow Lifecycle Metrics (Essential)
    private readonly Counter<long> _orchestrationFlowsStartedCounter;
    private readonly Counter<long> _orchestrationFlowsCompletedCounter;
    private readonly Counter<long> _orchestrationFlowsFailedCounter;

    // Cache Operations Metrics (Simplified)
    private readonly Counter<long> _cacheOperationsCounter;
    private readonly Gauge<long> _activeCacheEntriesGauge;

    // Health Metrics (Simplified)
    private readonly Gauge<int> _healthStatusGauge;
    private readonly Counter<long> _healthCheckCounter;

    // NEW: Flow Anomaly Detection Metric
    private readonly Gauge<long> _stepFlowAnomalyGauge;

    public OrchestratorMetricsService(
        IOptions<ManagerConfiguration> config,
        ILogger<OrchestratorMetricsService> logger,
        IOrchestratorMetricsLabelService labelService)
    {
        _config = config.Value;
        _logger = logger;
        _labelService = labelService;

        // Use the recommended unique meter name pattern: {Version}_{Name}
        var meterName = $"{_config.Version}_{_config.Name}";
        _meter = new Meter($"{meterName}.Orchestrator");

        // Initialize step publishing metrics (Core for Anomaly Detection)
        _stepCommandsPublishedCounter = _meter.CreateCounter<long>(
            "orchestrator_step_commands_published_total",
            "Total number of step commands published by the orchestrator");

        _stepCommandsSuccessfulCounter = _meter.CreateCounter<long>(
            "orchestrator_step_commands_successful_total",
            "Total number of step commands successfully published");

        _stepCommandsFailedCounter = _meter.CreateCounter<long>(
            "orchestrator_step_commands_failed_total",
            "Total number of step commands that failed to publish");

        _activeStepExecutionsGauge = _meter.CreateGauge<long>(
            "orchestrator_active_step_executions_total",
            "Current number of active step executions");

        // Initialize step consumption metrics (Core for Anomaly Detection)
        _activityEventsConsumedCounter = _meter.CreateCounter<long>(
            "orchestrator_activity_events_consumed_total",
            "Total number of activity events consumed by the orchestrator");

        _activityEventsSuccessfulCounter = _meter.CreateCounter<long>(
            "orchestrator_activity_events_successful_total",
            "Total number of activity events successfully processed");

        _activityEventsFailedCounter = _meter.CreateCounter<long>(
            "orchestrator_activity_events_failed_total",
            "Total number of activity events that failed processing");

        _flowBranchTerminationsCounter = _meter.CreateCounter<long>(
            "orchestrator_flow_branch_terminations_total",
            "Total number of flow branch terminations detected");

        // Initialize flow lifecycle metrics (Essential)
        _orchestrationFlowsStartedCounter = _meter.CreateCounter<long>(
            "orchestrator_flows_started_total",
            "Total number of orchestration flows started");

        _orchestrationFlowsCompletedCounter = _meter.CreateCounter<long>(
            "orchestrator_flows_completed_total",
            "Total number of orchestration flows completed");

        _orchestrationFlowsFailedCounter = _meter.CreateCounter<long>(
            "orchestrator_flows_failed_total",
            "Total number of orchestration flows that failed");

        // Initialize cache operations metrics (Simplified)
        _cacheOperationsCounter = _meter.CreateCounter<long>(
            "orchestrator_cache_operations_total",
            "Total number of cache operations performed");

        _activeCacheEntriesGauge = _meter.CreateGauge<long>(
            "orchestrator_active_cache_entries_total",
            "Current number of active cache entries");

        // Initialize health metrics (Simplified)
        _healthStatusGauge = _meter.CreateGauge<int>(
            "orchestrator_health_status",
            "Current health status of the orchestrator (0=Healthy, 1=Degraded, 2=Unhealthy)");

        _healthCheckCounter = _meter.CreateCounter<long>(
            "orchestrator_health_checks_total",
            "Total number of health checks performed");

        // Initialize flow anomaly detection metric (NEW)
        _stepFlowAnomalyGauge = _meter.CreateGauge<long>(
            "orchestrator_step_flow_anomaly_difference",
            "Absolute difference between published and consumed amounts per step (anomaly indicator)");

        _logger.LogInformationWithCorrelation(
            "OrchestratorMetricsService initialized with meter name: {MeterName}, Composite Key: {CompositeKey}",
            $"{meterName}.Orchestrator", _labelService.OrchestratorCompositeKey);
    }

    // Step Publishing Methods

    public void RecordStepCommandPublished(bool success, string stepVersionName)
    {
        var tags = _labelService.GetStepPublishLabels(stepVersionName, "publish_step_command", success ? "success" : "failed");

        _stepCommandsPublishedCounter.Add(1, tags);

        if (success)
            _stepCommandsSuccessfulCounter.Add(1, tags);
        else
            _stepCommandsFailedCounter.Add(1, tags);

        _logger.LogDebugWithCorrelation(
            "Recorded step command published metrics: StepVersionName={StepVersionName}, Success={Success}",
            stepVersionName, success);
    }



    public void UpdateActiveStepExecutions(long count)
    {
        var tags = _labelService.GetSystemLabels();
        _activeStepExecutionsGauge.Record(count, tags);

        _logger.LogDebugWithCorrelation(
            "Updated active step executions: Count={Count}",
            count);
    }

    // Step Consumption Methods

    public void RecordActivityEventProcessed(bool success, string stepVersionName, string orchestratedFlowVersionName, bool isFlowTermination = false)
    {
        var tags = _labelService.GetStepConsumptionLabels(stepVersionName, orchestratedFlowVersionName, "process_activity_event", success ? "success" : "failed");

        _activityEventsConsumedCounter.Add(1, tags);

        if (success)
            _activityEventsSuccessfulCounter.Add(1, tags);
        else
            _activityEventsFailedCounter.Add(1, tags);

        if (isFlowTermination)
            _flowBranchTerminationsCounter.Add(1, tags);

        _logger.LogDebugWithCorrelation(
            "Recorded activity event processed metrics: StepVersionName={StepVersionName}, OrchestratedFlowVersionName={OrchestratedFlowVersionName}, Success={Success}, IsFlowTermination={IsFlowTermination}",
            stepVersionName, orchestratedFlowVersionName, success, isFlowTermination);
    }

    // Flow Lifecycle Methods

    public void RecordOrchestrationFlow(bool success, string orchestratedFlowVersionName, string operation)
    {
        var tags = _labelService.GetFlowLabels(orchestratedFlowVersionName, operation, success ? "success" : "failed");

        // Record based on operation type
        switch (operation.ToLowerInvariant())
        {
            case "start":
                _orchestrationFlowsStartedCounter.Add(1, tags);
                break;
            case "complete":
                _orchestrationFlowsCompletedCounter.Add(1, tags);
                break;
            case "fail":
                _orchestrationFlowsFailedCounter.Add(1, tags);
                break;
        }

        _logger.LogDebugWithCorrelation(
            "Recorded orchestration flow metrics: OrchestratedFlowVersionName={OrchestratedFlowVersionName}, Operation={Operation}, Success={Success}",
            orchestratedFlowVersionName, operation, success);
    }

    // Cache Operations Methods

    public void RecordCacheOperation(bool success, string operationType)
    {
        var tags = _labelService.GetCacheLabels(operationType, success ? "success" : "failed");

        _cacheOperationsCounter.Add(1, tags);

        _logger.LogDebugWithCorrelation(
            "Recorded cache operation metrics: OperationType={OperationType}, Success={Success}",
            operationType, success);
    }

    public void UpdateActiveCacheEntries(long count)
    {
        var tags = _labelService.GetSystemLabels();
        _activeCacheEntriesGauge.Record(count, tags);

        _logger.LogDebugWithCorrelation(
            "Updated active cache entries: Count={Count}",
            count);
    }

    // Health Methods

    public void RecordHealthStatus(int status)
    {
        var tags = _labelService.GetSystemLabels();
        _healthStatusGauge.Record(status, tags);

        _logger.LogDebugWithCorrelation(
            "Recorded health status: Status={Status}",
            status);
    }

    public void RecordHealthCheck(string healthCheckName, string healthCheckStatus)
    {
        var tags = _labelService.GetHealthLabels(healthCheckName, healthCheckStatus);

        _healthCheckCounter.Add(1, tags);

        _logger.LogDebugWithCorrelation(
            "Recorded health check metrics: HealthCheckName={HealthCheckName}, Status={Status}",
            healthCheckName, healthCheckStatus);
    }

    // NEW: Flow Anomaly Detection Method
    public void RecordStepFlowAnomaly(string stepVersionName, long publishedCount, long consumedCount)
    {
        var difference = Math.Abs(publishedCount - consumedCount);
        var anomalyStatus = difference > 0 ? "anomaly_detected" : "healthy";
        var tags = _labelService.GetStepLabels(stepVersionName, "flow_anomaly", anomalyStatus);

        _stepFlowAnomalyGauge.Record(difference, tags);

        if (difference > 0)
        {
            _logger.LogWarningWithCorrelation(
                "🚨 Flow anomaly detected for step {StepVersionName}: Published={Published}, Consumed={Consumed}, Difference={Difference}",
                stepVersionName, publishedCount, consumedCount, difference);
        }
        else
        {
            _logger.LogDebugWithCorrelation(
                "Flow balance healthy for step {StepVersionName}: Published={Published}, Consumed={Consumed}",
                stepVersionName, publishedCount, consumedCount);
        }
    }

    public void Dispose()
    {
        _meter?.Dispose();
        _logger.LogInformationWithCorrelation("OrchestratorMetricsService disposed");
    }
}
