# Orchestrator Metrics Implementation Guide

## 1. UpdateActiveStepExecutions

### Location 1: OrchestrationService.cs - ExecuteOrchestrationAsync
**File**: `Managers\Manager.Orchestrator\Services\OrchestrationService.cs`
**Method**: `ExecuteOrchestrationAsync`
**Line**: After line 145 (after executing entry points)

```csharp
// Step 7: Execute entry point steps
publishedCommands = orchestrationData.EntryPoints.Count;

// Update active step executions metric
_metricsService.UpdateActiveStepExecutions(publishedCommands);

// Record entry point execution metrics
_metricsService.RecordEntryPointExecution(
    success: true,
    duration: stopwatch.Elapsed,
    orchestratedFlowVersionName: orchestratedFlow.GetCompositeKey(),
    entryPointCount: entryPointCount);
```

### Location 2: ActivityExecutedEventConsumer.cs - ProcessNextStepsAsync
**File**: `Managers\Manager.Orchestrator\Consumers\ActivityExecutedEventConsumer.cs`
**Method**: `ProcessNextStepsAsync`
**Line**: After line 282 (after publishing step command)

```csharp
// Record step command published metrics
_metricsService.RecordStepCommandPublished(
    success: true,
    duration: publishStopwatch.Elapsed,
    stepVersionName: nextStepEntity.GetCompositeKey(),
    entitiesCount: assignmentModels.Count);

// Update active step executions (increment by 1 for each new step)
_metricsService.UpdateActiveStepExecutions(1);
```

### Location 3: ActivityExecutedEventConsumer.cs - HandleAsync (Flow Termination)
**File**: `Managers\Manager.Orchestrator\Consumers\ActivityExecutedEventConsumer.cs`
**Method**: `HandleAsync`
**Line**: After line 118 (when flow terminates - nextSteps.Count == 0)

```csharp
// Record activity event processed metrics
_metricsService.RecordActivityEventProcessed(
    success: true,
    duration: stopwatch.Elapsed,
    stepVersionName: currentStepEntity.GetCompositeKey(),
    orchestratedFlowVersionName: orchestrationData.OrchestratedFlow.GetCompositeKey(),
    nextStepsCount: nextSteps.Count,
    isFlowTermination: nextSteps.Count == 0);

// Update active step executions (decrement when flow terminates)
if (nextSteps.Count == 0)
{
    _metricsService.UpdateActiveStepExecutions(-1);
}
```

## 2. RecordCacheOperation & UpdateActiveCacheEntries

### Location 1: OrchestrationCacheService.cs - StoreOrchestrationDataAsync
**File**: `Managers\Manager.Orchestrator\Services\OrchestrationCacheService.cs`
**Method**: `StoreOrchestrationDataAsync`

**Add constructor injection**:
```csharp
private readonly IOrchestratorMetricsService _metricsService;

public OrchestrationCacheService(
    ICacheService cacheService,
    IConfiguration configuration,
    ILogger<OrchestrationCacheService> logger,
    IOrchestratorMetricsService metricsService)
{
    _cacheService = cacheService;
    _configuration = configuration;
    _logger = logger;
    _metricsService = metricsService;
    // ... rest of constructor
}
```

**Add metrics after line 55**:
```csharp
try
{
    var stopwatch = Stopwatch.StartNew();
    var cacheValue = JsonSerializer.Serialize(orchestrationData, _jsonOptions);
    await StoreWithRetryAsync(cacheKey, cacheValue, effectiveTtl);
    stopwatch.Stop();

    // Record cache operation metrics
    _metricsService.RecordCacheOperation(
        success: true,
        duration: stopwatch.Elapsed,
        operationType: "store");

    // Update active cache entries count (increment)
    _metricsService.UpdateActiveCacheEntries(1);

    _logger.LogInformationWithCorrelation("Successfully stored orchestration data in cache...");
}
catch (Exception ex)
{
    // Record failed cache operation
    _metricsService.RecordCacheOperation(
        success: false,
        duration: TimeSpan.Zero,
        operationType: "store");
    
    _logger.LogErrorWithCorrelation(ex, "Failed to store orchestration data in cache...");
    throw;
}
```

### Location 2: OrchestrationCacheService.cs - GetOrchestrationDataAsync
**Add metrics after line 92**:
```csharp
var stopwatch = Stopwatch.StartNew();
var orchestrationData = JsonSerializer.Deserialize<OrchestrationCacheModel>(cacheValue, _jsonOptions);
stopwatch.Stop();

// Record cache operation metrics
_metricsService.RecordCacheOperation(
    success: orchestrationData != null,
    duration: stopwatch.Elapsed,
    operationType: "get");

if (orchestrationData == null)
{
    _logger.LogWarningWithCorrelation("Failed to deserialize orchestration data from cache...");
    return null;
}
```

### Location 3: OrchestrationCacheService.cs - RemoveOrchestrationDataAsync
**Add metrics in the method**:
```csharp
public async Task RemoveOrchestrationDataAsync(Guid orchestratedFlowId)
{
    var cacheKey = orchestratedFlowId.ToString();
    var stopwatch = Stopwatch.StartNew();

    try
    {
        await _cacheService.RemoveAsync(_mapName, cacheKey);
        stopwatch.Stop();

        // Record cache operation metrics
        _metricsService.RecordCacheOperation(
            success: true,
            duration: stopwatch.Elapsed,
            operationType: "remove");

        // Update active cache entries count (decrement)
        _metricsService.UpdateActiveCacheEntries(-1);

        _logger.LogInformationWithCorrelation("Successfully removed orchestration data from cache...");
    }
    catch (Exception ex)
    {
        stopwatch.Stop();

        // Record failed cache operation
        _metricsService.RecordCacheOperation(
            success: false,
            duration: stopwatch.Elapsed,
            operationType: "remove");

        _logger.LogErrorWithCorrelation(ex, "Failed to remove orchestration data from cache...");
        throw;
    }
}
```

## 3. RecordHealthStatus & RecordHealthCheck

### Location 1: Create OrchestratorHealthMonitor Service
**File**: `Managers\Manager.Orchestrator\Services\OrchestratorHealthMonitor.cs` (NEW FILE)

```csharp
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Hosting;
using Manager.Orchestrator.Services;
using System.Diagnostics;

namespace Manager.Orchestrator.Services;

public class OrchestratorHealthMonitor : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IOrchestratorMetricsService _metricsService;
    private readonly ILogger<OrchestratorHealthMonitor> _logger;
    private readonly TimeSpan _healthCheckInterval = TimeSpan.FromSeconds(30);

    public OrchestratorHealthMonitor(
        IServiceProvider serviceProvider,
        IOrchestratorMetricsService metricsService,
        ILogger<OrchestratorHealthMonitor> logger)
    {
        _serviceProvider = serviceProvider;
        _metricsService = metricsService;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await PerformHealthChecksAsync();
                await Task.Delay(_healthCheckInterval, stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in orchestrator health monitoring");
                await Task.Delay(_healthCheckInterval, stoppingToken);
            }
        }
    }

    private async Task PerformHealthChecksAsync()
    {
        using var scope = _serviceProvider.CreateScope();
        var healthCheckService = scope.ServiceProvider.GetRequiredService<HealthCheckService>();

        var stopwatch = Stopwatch.StartNew();
        var healthReport = await healthCheckService.CheckHealthAsync();
        stopwatch.Stop();

        // Record overall health status
        var overallStatus = healthReport.Status switch
        {
            HealthStatus.Healthy => 0,
            HealthStatus.Degraded => 1,
            HealthStatus.Unhealthy => 2,
            _ => 2
        };

        _metricsService.RecordHealthStatus(overallStatus);

        // Record individual health checks
        foreach (var entry in healthReport.Entries)
        {
            var healthCheckStatus = entry.Value.Status switch
            {
                HealthStatus.Healthy => "healthy",
                HealthStatus.Degraded => "degraded",
                HealthStatus.Unhealthy => "unhealthy",
                _ => "unknown"
            };

            _metricsService.RecordHealthCheck(
                healthCheckName: entry.Key,
                healthCheckStatus: healthCheckStatus,
                duration: entry.Value.Duration);
        }
    }
}
```

### Location 2: Register Health Monitor in Program.cs
**File**: `Managers\Manager.Orchestrator\Program.cs`
**Add after line 84**:

```csharp
// Add orchestration services
builder.Services.AddScoped<IOrchestrationCacheService, OrchestrationCacheService>();
builder.Services.AddScoped<IOrchestrationService, OrchestrationService>();
builder.Services.AddScoped<IOrchestrationSchedulerService, OrchestrationSchedulerService>();

// Add orchestrator health monitoring
builder.Services.AddHostedService<OrchestratorHealthMonitor>();
```

## 4. Summary of Changes Required

### Files to Modify:
1. **OrchestrationService.cs** - Add UpdateActiveStepExecutions calls
2. **ActivityExecutedEventConsumer.cs** - Add UpdateActiveStepExecutions calls
3. **OrchestrationCacheService.cs** - Add RecordCacheOperation and UpdateActiveCacheEntries calls
4. **Program.cs** - Register OrchestratorHealthMonitor service

### New File to Create:
1. **OrchestratorHealthMonitor.cs** - Background service for health monitoring

### Expected Results:
After implementing these changes, all missing metrics will be emitted:
- ✅ `orchestrator_active_step_executions_total`
- ✅ `orchestrator_active_cache_entries_total`
- ✅ `orchestrator_cache_operations_total`
- ✅ `orchestrator_health_status`
- ✅ `orchestrator_health_checks_total`

The dashboard will then show data for all panels! 🎉
```
